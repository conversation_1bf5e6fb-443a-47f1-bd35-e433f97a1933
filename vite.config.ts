import { sveltekit } from '@sveltejs/kit/vite'
import { defineConfig } from 'vite'

const proxyValue = {
  target: 'http://int-lweb-stg.aws01.mapion.co.jp',
  changeOrigin: true,
}

const localhostProxyValue = {
  target: 'http://localhost:80',
  changeOrigin: true,
  secure: false,
}

export default defineConfig({
  plugins: [sveltekit()],
  server: {
    proxy: {
      '/map/': proxyValue,
      '/GetGrpList': localhostProxyValue,
    }
  }
})