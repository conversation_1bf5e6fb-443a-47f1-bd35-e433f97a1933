<script lang="ts">
  import { config } from "$lib/config";
  import Sidenavi from "$lib/components/sidenavi.svelte";
</script>

<div class="drawer">
  <div class="drawer-content flex flex-col">
    <div class="navbar bg-base-300 w-full">
      <div class="flex-none lg:hidden">
        <label
          for="my-drawer-2"
          aria-label="open sidebar"
          class="btn btn-square btn-ghost"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            class="inline-block h-6 w-6 stroke-current"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 6h16M4 12h16M4 18h16"
            ></path>
          </svg>
        </label>
      </div>
      <div class="mx-2 flex-1 px-2 text-2xl font-bold">{config.head_title}</div>
      <div class="hidden flex-none lg:block">
        <ul class="menu menu-horizontal">
          <!-- Navbar menu content here -->
          <li><a>〇〇さん</a></li>
          <li><a>ログアウト</a></li>
        </ul>
      </div>
    </div>
  </div>
</div>
