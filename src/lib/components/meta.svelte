<script lang="ts">
    import { config } from '$lib/config';
  
    export let title: string;
    export let description: string;
    export let type: string;
    export let image: string;
  
  </script>
  
  <svelte:head>
    <meta name="robots" content="index,follow">
    <meta name="googlebot" content="index,follow">
    <title>{config.head_title}{title}</title>
    <meta name="description" content={config.head_description}{description} />
  
    <meta property=”og:url” content="{config.brand_url}" />
    <meta property=”og:type” content="{type}" />
    <meta property=”og:title” content="{config.head_title}{title}" />
    <meta property=”og:description” content="{config.head_description}{description}" />
    <meta property=”og:site_name” content="{config.brand}" />
    <meta property=”og:image” content="{image}" />
  </svelte:head>
  