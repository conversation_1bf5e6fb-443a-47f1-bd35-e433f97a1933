export interface ResponseGrp {
  [key: string]: string; // 動的に任意のプロパティを許容
}

export interface ResponseError {
  message: string;
}

export interface GrpListResponse {
  grps: ResponseGrp[];
  error?: ResponseError;
}

type GrpParams = {
  uid: string;
  bim_token: string;
}

/**
 * 配列か単一の値を受け取り、配列に変換して返す
 */
export const packToArray = <T>(arr: T|T[]):T[] => {
    return Array.isArray(arr)?arr:[arr]
}

/**
 * GRP一覧を取得するAPI関数
 */
export const fetchGrpList = async ({
  uid = 't_matsuyama',
  bim_token = 'BBBBB'
}: Partial<GrpParams> = {}) => {
  // Fetch data from API
  const api = "/GetGrpList";
  const params: GrpParams = {
    uid,
    bim_token
  };

  try {
    const url = `${api}?${new URLSearchParams(params).toString()}`;
    console.log('Fetching GRP list from:', url);

    const res = await fetch(url);
    console.log('Response status:', res.status, res.statusText);

    if (!res.ok) {
      throw new Error(`Network response was not ok: ${res.status} ${res.statusText}`);
    }

    const data = await res.json();
    console.log('GRP List API Response:', data);

    // APIレスポンスの構造に応じて調整が必要な場合があります
    // 仮の構造として、dataが配列またはオブジェクトを想定
    let grps: ResponseGrp[] = [];

    if (Array.isArray(data)) {
      grps = data;
    } else if (data && typeof data === 'object') {
      // データ構造に応じて適切なプロパティを選択
      if (data.grps) {
        grps = packToArray(data.grps);
      } else if (data.groups) {
        grps = packToArray(data.groups);
      } else if (data.list) {
        grps = packToArray(data.list);
      } else {
        // データ全体を配列として扱う
        grps = [data];
      }
    }

    return {
      grps,
    };
  } catch (error) {
    console.error("Error fetching GRP list:", error);
    return {
      grps: [],
      error: {
        message: `Error fetching GRP list: ${error}`,
      },
    };
  }
};
