export interface ResponseGrp {
  [key: string]: string; // 動的に任意のプロパティを許容
}

export interface ResponseError {
  message: string;
}

export interface GrpListResponse {
  grps: ResponseGrp[];
  error?: ResponseError;
}

type GrpParams = {
  uid: string;
  bim_token: string;
}

/**
 * 配列か単一の値を受け取り、配列に変換して返す
 */
export const packToArray = <T>(arr: T|T[]):T[] => {
    return Array.isArray(arr)?arr:[arr]
}

/**
 * GRP一覧を取得するAPI関数
 */
export const fetchGrpList = async ({
  uid = 't_matsuyama',
  bim_token = 'BBBBB'
}: Partial<GrpParams> = {}) => {
  // Fetch data from API
  const api = "/GetGrpList";
  const params: GrpParams = {
    uid,
    bim_token
  };

  try {
    const url = `${api}?${new URLSearchParams(params).toString()}`;
    console.log('Fetching GRP list from:', url);

    const res = await fetch(url);
    console.log('Response status:', res.status, res.statusText);

    if (!res.ok) {
      throw new Error(`Network response was not ok: ${res.status} ${res.statusText}`);
    }

    const text = await res.text();
    console.log('GRP List API Raw Response:', text);
    console.log('Response length:', text.length);
    console.log('First 50 characters:', text.substring(0, 50));
    console.log('Last 50 characters:', text.substring(text.length - 50));

    let data;
    try {
      // まず、そのままJSONとしてパースを試行
      data = JSON.parse(text);
      console.log('Direct JSON parse successful');
    } catch (directParseError) {
      console.log('Direct JSON parse failed:', directParseError);

      try {
        // POI APIと同様に、不要な部分を削除してJSONをパース
        const str = text.trim().slice(1, -2);
        console.log('Trimmed string:', str);
        console.log('Trimmed string length:', str.length);
        data = JSON.parse(str);
        console.log('Trimmed JSON parse successful');
      } catch (trimmedParseError) {
        console.log('Trimmed JSON parse failed:', trimmedParseError);

        // 他の可能性を試す
        try {
          // 先頭と末尾の1文字だけ削除
          const str2 = text.trim().slice(1, -1);
          console.log('Alternative trimmed string:', str2);
          data = JSON.parse(str2);
          console.log('Alternative trimmed JSON parse successful');
        } catch (altParseError) {
          console.log('Alternative trimmed JSON parse failed:', altParseError);
          throw new Error(`Failed to parse JSON response. Raw response: ${text.substring(0, 200)}...`);
        }
      }
    }

    console.log('GRP List API Parsed Response:', data);

    // mbml.GrpList.grp の構造に対応
    let grps: ResponseGrp[] = [];

    if (data && data.mbml && data.mbml.GrpList && data.mbml.GrpList.grp) {
      grps = packToArray(data.mbml.GrpList.grp);
      console.log('Extracted GRP list:', grps);
    } else {
      console.warn('Expected mbml.GrpList.grp structure not found in response');
      // フォールバック: データ全体を確認
      console.log('Full data structure:', data);
    }

    return {
      grps,
    };
  } catch (error) {
    console.error("Error fetching GRP list:", error);
    return {
      grps: [],
      error: {
        message: `Error fetching GRP list: ${error}`,
      },
    };
  }
};
