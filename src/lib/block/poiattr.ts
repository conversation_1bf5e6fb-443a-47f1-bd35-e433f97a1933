import { config } from '$lib/config';

export interface ResponsePoi {
  [key: string]: string; // 動的に任意のプロパティを許容
}
export interface ResponseError {
  message: string;
}

type Params = {
  [key: string]: string
} & {
  code: string
  dtm?: string
  grp?: string
  oe?: string
  vo?: 'mbml'
  json?: string
  xml?: string
  srt?: string
  pm?: string
  start?: string
}

/**
 * 配列か単一の値を受け取り、配列に変換して返す
 */
export const packToArray = <T>(arr: T|T[]):T[] => {
    return Array.isArray(arr)?arr:[arr]
}

export const fetchAllPoiAttr = async ({
  poiCode,
}: { poiCode?: string | null } = {}) => {
  // Fetch data from API
  const api = "http://int-lweb-stg.aws01.mapion.co.jp/map/uc/PoiAttr";
  const params: Params = {
    grp: config.grp,
    dtm: config.dtm,
    pm: config.pm,
    poi_status: "1",
    code: "0",
    vo: "mbml",
    json: "1",
  };
  // poiCodeが空またはundefinedでない場合にpoi_codeをparamsに追加
  if (poiCode) {
    params.poi_code = poiCode;
  }

  try {
    const res = await fetch(`${api}?${new URLSearchParams(params).toString()}`);
    if (!res.ok) {
      throw new Error("Network response was not ok");
    }

    const text = await res.text();
    const str = text.trim().slice(1, -2); // 不要な部分を削除
    const json = JSON.parse(str);
    console.log(json);
    return {
      poi: packToArray(json.mbml.PoiList.Poi),
      hit: packToArray(json.mbml.Property.hit),
    };
  } catch (error) {
    console.error("Error fetching POI attributes:", error);
    return {
      error: {
        message: `Error fetching POI attributes:${error}`,
      },
    };
  }
};