<script lang="ts">
  import Meta from "$lib/components/meta.svelte";
  import type { ResponseGrp } from '$lib/block/grplist';

  // 受け取ったgrpListを内部で管理するための変数
  let error: any; // エラー管理
  let searchTerm = ''; // 検索用の変数

  // page.server.ts で定義したデータを取得
  export let data: any;
  if (data.grpList.error) {
    error = data.grpList.error;
  }
  let grpList = data.grpList.grps || [];

  // 検索フィルタリング機能
  $: filteredGrps = grpList.filter((grp: ResponseGrp) => {
    if (!searchTerm) return true;
    const searchLower = searchTerm.toLowerCase();
    return Object.values(grp).some(value => 
      value && value.toString().toLowerCase().includes(searchLower)
    );
  });
</script>

<Meta
  title="GRP一覧ページ"
  description="GRP一覧ページディスクリプション"
  type="website"
  image="https://example.com/image.jpg"
/>

<h2 class="text-xl font-bold">GRP一覧</h2>
<p>GRPデータの一覧を表示します</p>

<div class="mt-10 p-5 bg-white">
  <h3 class="text-l font-bold border-b-2 w-full">GRPを検索する</h3>
  <div class="mt-5">
    <input
      type="text"
      placeholder="GRP名またはIDを入力"
      class="input input-bordered input-primary max-w-xs md:w-full"
      bind:value={searchTerm}
    />
    <button class="btn btn-primary ml-2" type="button">
      <i class="bi bi-search" style="font-size: 1.2rem;"></i>
    </button>
  </div>
</div>

<div class="mt-10 p-5 bg-white">
  <!-- エラーが発生した場合の表示 -->
  {#if error}
    <p class="text-red-500">エラーが発生しました: {error.message}</p>
  {/if}

  {#if !error}
    <div class="mb-4">
      <p class="text-sm text-gray-600">検索結果: {filteredGrps.length}件</p>
    </div>

    {#if filteredGrps.length === 0}
      <div class="text-center py-10">
        <p class="text-gray-500">該当するGRPが見つかりませんでした。</p>
      </div>
    {:else}
      {#each filteredGrps as grp, index}
        <div class="bg-base-100 shadow mb-2">
          <div class="card-body p-4 md:flex md:flex-row md:w-full md:p-5">
            <!-- GRP ID -->
            <div class="md:w-1/4">
              <p class="text-xs text-gray-500">GRP ID</p>
              <p class="text-sm font-medium">{grp.id || grp.grp_id || grp.code || `GRP-${index + 1}`}</p>
            </div>
            
            <!-- GRP名 -->
            <div class="md:w-2/4">
              <p class="text-xs text-gray-500">GRP名</p>
              <h2 class="card-title text-base">{grp.name || grp.grp_name || grp.title || 'GRP名未設定'}</h2>
            </div>

            <!-- ステータス -->
            <div class="md:w-1/4">
              <p class="text-xs text-gray-500">ステータス</p>
              <div class="flex items-center">
                {#if grp.status === "1" || grp.active === "1" || grp.enabled === "true"}
                  <span class="border border-green-500 px-2 py-1 text-xs rounded text-green-500 text-nowrap">
                    有効
                  </span>
                {:else if grp.status === "0" || grp.active === "0" || grp.enabled === "false"}
                  <span class="border border-gray-500 px-2 py-1 text-xs rounded text-gray-500 text-nowrap">
                    無効
                  </span>
                {:else}
                  <span class="border border-blue-500 px-2 py-1 text-xs rounded text-blue-500 text-nowrap">
                    不明
                  </span>
                {/if}
              </div>
            </div>

            <!-- アクションボタン -->
            <div class="card-actions justify-end md:w-1/4 mt-4 md:mt-0">
              <button
                type="button"
                class="btn btn-accent btn-sm"
                on:click={() => {
                  // 詳細ボタンがクリックされたときの処理
                  console.log('GRP詳細:', grp);
                }}
              >
                <i class="bi bi-eye" style="font-size: 0.9rem;"></i>
                詳細
              </button>

              <button 
                type="button" 
                class="btn btn-primary btn-sm"
                on:click={() => {
                  // 編集ボタンがクリックされたときの処理
                  console.log('GRP編集:', grp);
                }}
              >
                <i class="bi bi-pencil" style="font-size: 0.9rem;"></i>
                編集
              </button>
            </div>
          </div>

          <!-- 詳細情報（折りたたみ可能） -->
          <div class="collapse collapse-arrow">
            <input type="checkbox" />
            <div class="collapse-title text-sm font-medium">
              詳細情報を表示
            </div>
            <div class="collapse-content">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                {#each Object.entries(grp) as [key, value]}
                  <div>
                    <span class="font-medium text-gray-600">{key}:</span>
                    <span class="ml-2">{value || '未設定'}</span>
                  </div>
                {/each}
              </div>
            </div>
          </div>
        </div>
      {/each}
    {/if}
  {/if}

  <!-- デバッグ用：生データ表示（開発時のみ） -->
  <!-- 
  <div class="mt-10 p-5 bg-gray-100">
    <h4 class="font-bold">デバッグ情報（開発時のみ）</h4>
    <pre class="text-xs overflow-auto">{JSON.stringify(grpList, null, 2)}</pre>
  </div>
  -->
</div>
