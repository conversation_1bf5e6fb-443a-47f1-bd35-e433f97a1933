<script lang="ts">
  import Meta from "$lib/components/meta.svelte";
  import { fetchGrpList } from "$lib/block/grplist";
  import type { ResponseGrp } from '$lib/block/grplist';
  import { onMount } from "svelte";

  // 状態管理用の変数
  let grpList: ResponseGrp[] = [];
  let loading = true; // データロード中かどうか
  let error: any = null; // エラー管理
  let searchTerm = ''; // 検索用の変数

  // onMount フック内でデータを非同期に取得
  onMount(async () => {
    try {
      const result = await fetchGrpList();
      if (result.error) {
        error = result.error;
        grpList = [];
      } else {
        grpList = result.grps;
        error = null;
      }
    } catch (err) {
      error = { message: `予期しないエラーが発生しました: ${err}` };
      grpList = [];
    } finally {
      loading = false;
    }
  });

  // 検索フィルタリング機能（grpとnameフィールドに特化）
  $: filteredGrps = grpList.filter((grp: ResponseGrp) => {
    if (!searchTerm) return true;
    const searchLower = searchTerm.toLowerCase();

    // grpフィールドとnameフィールドで検索
    const grpValue = (grp.grp || '').toString().toLowerCase();
    const nameValue = (grp.name || '').toString().toLowerCase();

    return grpValue.includes(searchLower) || nameValue.includes(searchLower);
  });

  // リフレッシュ機能
  const refreshData = async () => {
    loading = true;
    error = null;
    try {
      const result = await fetchGrpList();
      if (result.error) {
        error = result.error;
        grpList = [];
      } else {
        grpList = result.grps;
        error = null;
      }
    } catch (err) {
      error = { message: `予期しないエラーが発生しました: ${err}` };
      grpList = [];
    } finally {
      loading = false;
    }
  };
</script>

<Meta
  title="GRP一覧ページ"
  description="GRP一覧ページディスクリプション"
  type="website"
  image="https://example.com/image.jpg"
/>

<div class="flex justify-between items-center">
  <div>
    <h2 class="text-xl font-bold">GRP一覧</h2>
    <p>GRPデータの一覧を表示します</p>
  </div>
  <button
    class="btn btn-outline btn-sm"
    type="button"
    on:click={refreshData}
    disabled={loading}
  >
    <i class="bi bi-arrow-clockwise" style="font-size: 1rem;"></i>
    更新
  </button>
</div>

<!-- ローディング中は表示 -->
{#if loading}
  <div class="mt-10 p-5 bg-white">
    <div class="flex justify-center items-center py-10">
      <span class="loading loading-spinner loading-lg"></span>
      <span class="ml-3">データを読み込み中...</span>
    </div>
  </div>
{:else}
  <div class="mt-10 p-5 bg-white">
    <h3 class="text-l font-bold border-b-2 w-full">GRPを検索する</h3>
    <div class="mt-5">
      <input
        type="text"
        placeholder="GRP IDまたはGRP名を入力"
        class="input input-bordered input-primary max-w-xs md:w-full"
        bind:value={searchTerm}
      />
      <button
        class="btn btn-primary ml-2"
        type="button"
        aria-label="検索"
      >
        <i class="bi bi-search" style="font-size: 1.2rem;"></i>
      </button>
    </div>
  </div>
{/if}

{#if !loading}
  <div class="mt-10 p-5 bg-white">
    <!-- エラーが発生した場合の表示 -->
    {#if error}
      <p class="text-red-500">エラーが発生しました: {error.message}</p>
    {/if}

    {#if !error}
      <div class="mb-4">
        <p class="text-sm text-gray-600">検索結果: {filteredGrps.length}件</p>
      </div>

      {#if filteredGrps.length === 0}
        <div class="text-center py-10">
          <p class="text-gray-500">該当するGRPが見つかりませんでした。</p>
        </div>
      {:else}
        <!-- テーブル形式のリスト（デスクトップ向け） -->
        <div class="hidden md:block overflow-x-auto">
          <table class="table table-zebra w-full">
            <thead>
              <tr>
                <th class="w-1/12">#</th>
                <th class="w-5/12">GRP ID</th>
                <th class="w-6/12">GRP名</th>
              </tr>
            </thead>
            <tbody>
              {#each filteredGrps as grp, index}
                <tr class="hover">
                  <td class="text-sm text-gray-500">{index + 1}</td>
                  <td>
                    <div class="font-mono text-sm font-medium">
                      {grp.grp || grp.id || grp.code || `GRP-${index + 1}`}
                    </div>
                  </td>
                  <td>
                    <div class="font-medium">
                      {grp.name || 'GRP名未設定'}
                    </div>
                  </td>
                </tr>
              {/each}
            </tbody>
          </table>
        </div>

        <!-- カード形式のリスト（モバイル向け） -->
        <div class="block md:hidden">
          {#each filteredGrps as grp, index}
            <div class="card bg-base-100 shadow-sm mb-3">
              <div class="card-body p-4">
                <div class="flex justify-between items-start">
                  <div class="flex-1">
                    <div class="text-xs text-gray-500 mb-1">#{index + 1}</div>
                    <div class="font-mono text-sm font-medium text-primary mb-2">
                      {grp.grp || grp.id || grp.code || `GRP-${index + 1}`}
                    </div>
                    <div class="font-medium text-base">
                      {grp.name || 'GRP名未設定'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          {/each}
        </div>
      {/if}
    {/if}

    <!-- デバッグ用：生データ表示（開発時のみ） -->
    <div class="mt-10 p-5 bg-gray-100">
      <h4 class="font-bold">デバッグ情報（開発時のみ）</h4>
      <div class="mt-3">
        <p><strong>Loading:</strong> {loading}</p>
        <p><strong>Error:</strong> {error ? error.message : 'なし'}</p>
        <p><strong>GRP List Length:</strong> {grpList.length}</p>
        <p><strong>Filtered GRPs Length:</strong> {filteredGrps.length}</p>
      </div>
      <div class="mt-3">
        <h5 class="font-semibold">Raw GRP List:</h5>
        <pre class="text-xs overflow-auto bg-white p-2 rounded">{JSON.stringify(grpList, null, 2)}</pre>
      </div>
      {#if filteredGrps.length > 0}
        <div class="mt-3">
          <h5 class="font-semibold">First Filtered GRP:</h5>
          <pre class="text-xs overflow-auto bg-white p-2 rounded">{JSON.stringify(filteredGrps[0], null, 2)}</pre>
        </div>
      {/if}
    </div>
  </div>
{/if}
