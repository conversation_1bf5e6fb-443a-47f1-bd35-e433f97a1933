<script lang="ts">
  import Meta from "$lib/components/meta.svelte";
  import { fetchAllPoiAttr } from "$lib/block/poiattr"; // poiattrのデータを取得する関数 fetchAllPoiAttr をインポート }
  import { onMount } from "svelte";
  // import type { PageData } from './$types';
  import type { ResponsePoi } from '$lib/block/poiattr';
  import CommonPoiSetting from "../CommonPoiSetting.svelte";
  import LocationSetting from "../LocationSetting.svelte";
  import IndivPoiSetting from "../IndivPoiSetting.svelte";
  import PublishSetting from "../PublishSetting.svelte";

  // export let data: PageData;

  // 受け取ったpoiattrを内部で管理するための変数
  let poiattr:{
  pois: ResponsePoi[],
  hits: number
} = { pois: [], hits: 0 };
  let loading = true; // データロード中かどうか
  let error: any; // エラー管理
  let poi: ResponsePoi = {
    kencode: "",
    poi_status: "0",
    citycode: "",
    kind: "",
    crowd_permit_flag: "",
    cityname: "",
    latitude:  "",
    longitude:  "",
    name:  "",
    kenname:  "",
    id:  "",
    full_address:  "",

  }; // ページに表示するpoiデータ
  // let hit: number = 0; // ページに表示する検索結果数
  // onMount フック内でデータを非同期に取得
  // onMount(async () => {
  //   try {
  //     poiattr = await fetchAllPoiAttr({poiCode:data.post.poiCode}); // poiattrのデータを取得
  //     poi = poiattr.pois[0];
  //     hit = poiattr.hits;
  //   } catch (err) {
  //     error = err;
  //     poiattr = { pois: [], hits: 0 }; // エラー発生時は空の配列に
  //   } finally {
  //     loading = false; // ロード終了
  //   }
  // });
  poiattr = { pois: [], hits: 0 };
  loading = false;
  error = null;

</script>

<Meta
  title="店舗データ新規登録"
  description="トップページディスクリプション"
  type="website"
  image="https://example.com/image.jpg"
/>

<h2 class="text-xl font-bold">データの新規登録</h2>
<p>データの新規登録を行います。</p>
  
<!-- ローディング中は表示 -->
{#if loading}
  <p>データを読み込み中...</p>
{/if}

<!-- エラーが発生した場合の表示 -->
{#if error}
  <p class="text-red-500">エラーが発生しました: {error.message}</p>
{/if}

<!-- poiattr が取得できた場合にデータを表示 -->

{#if !loading && !error}
  <div class="mt-10 p-5 bg-white">
    <p>POIデータ: {JSON.stringify(poi)}</p>
    <!-- その他のフォームの内容 -->
  </div>
{/if}

{#if !loading && !error && !poi}
  <p>該当するデータが見つかりませんでした。</p>
{/if}

{#if !loading && !error && poi}
<CommonPoiSetting {poi} />
<LocationSetting {poi} />
<IndivPoiSetting {poi} />
<PublishSetting {poi} />
{/if}
