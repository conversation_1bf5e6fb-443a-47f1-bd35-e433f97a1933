<script lang="ts">
import type { ResponsePoi } from '$lib/block/poiattr';

export let poi: ResponsePoi;

</script>
{poi.name}
<div class="mt-10 p-5 bg-white">
    <h3 class="text-l font-bold border-b-2 w-full">POI設定</h3>
  
    <div class="mt-5">
      <div class="md:flex w-full">
        <label class="label flex flex-col md:w-2/5 items-start">
          <span class="label-text">店舗コード</span>
          <p class="text-xs text-gray-500">店舗を識別するためのコードです</p>
        </label>
        <input
          type="text"
          class="input input-bordered input-bordered input-primary w-full md:w-3/5"
          placeholder="店舗コード"
          bind:value={poi.id}
        />
      </div>
      <p class="text-xs text-gray-500 mt-2 text-right">
        ※店舗コードは半角英数字で入力してください
      </p>
    </div>
    <div class="mt-5">
      <div class="md:flex w-full">
        <label class="label flex flex-col md:w-2/5 items-start">
          <span class="label-text">店舗名</span>
          <p class="text-xs text-gray-500">店舗名を入力してください</p>
        </label>
        <input
          type="text"
          class="input input-bordered input-bordered input-primary w-full md:w-3/5"
          placeholder="店舗名"
          bind:value={poi.name}
        />
      </div>
      <p class="text-xs text-gray-500 mt-2 text-right">
        ※文字列で入力してください
      </p>
    </div>
  </div>