<script lang="ts">
  import type { ResponsePoi } from '$lib/block/poiattr';

  export let poi: ResponsePoi;
</script>

<div class="mt-10 p-5 bg-white">
    <h3 class="text-l font-bold border-b-2 w-full">位置情報（緯度経度）設定</h3>
  
    <div class="lat mt-5">
      <div class="md:flex w-full">
        <label class="label flex flex-col md:w-2/5 items-start">
          <span class="label-text">緯度</span>
          <p class="text-xs text-gray-500">緯度を入力してください</p>
        </label>
        <input
          type="text"
          class="input input-bordered input-bordered input-primary w-full md:w-3/5"
          placeholder="緯度"
          bind:value={poi.latitude}
        />
      </div>
      <p class="text-xs text-gray-500 mt-2 text-right">(例) 35.681236</p>
    </div>
    <div class="lng mt-5">
      <div class="md:flex w-full">
        <label class="label flex flex-col md:w-2/5 items-start">
          <span class="label-text">経度</span>
          <p class="text-xs text-gray-500">経度を入力してください</p>
        </label>
        <input
          type="text"
          class="input input-bordered input-bordered input-primary w-full md:w-3/5"
          placeholder="経度"
          bind:value={poi.longitude}
        />
      </div>
      <p class="text-xs text-gray-500 mt-2 text-right">(例) 139.767125</p>
    </div>
  </div>