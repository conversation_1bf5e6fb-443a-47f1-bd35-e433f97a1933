<script lang="ts">
  import Meta from "$lib/components/meta.svelte";

  // 受け取ったpoiattrを内部で管理するための変数
  let error: any; // エラー管理

  // page.server.ts で定義したデータを取得
  export let data: any;
  if (data.poiAttr.error) {
    error = data.poiAttr.error;
  }
  let poiattr = { 
    pois: data.poiAttr.poi || [],
    hits: data.poiAttr.hit || 0
  };

</script>

<Meta
  title="店舗一覧ページ"
  description="トップページディスクリプション"
  type="website"
  image="https://example.com/image.jpg"
/>

<h2 class="text-xl font-bold">店舗一覧</h2>
<p>店舗データの一覧を表示します</p>

<div class="mt-10 p-5 bg-white">
  <h3 class="text-l font-bold border-b-2 w-full">店舗を検索する</h3>
  <form action="poi" method="post">
    <input
      type="text"
      placeholder="店舗IDまたは店舗名を入力"
      class="input input-bordered input-primary max-w-xs mt-5 md:w-full"
    />
    <button class="btn btn-primary" type="submit">
      <i class="bi bi-search" style="font-size: 1.2rem;"></i></button
    >
  </form>
</div>

<div class="mt-10 p-5 bg-white">
  <!-- エラーが発生した場合の表示 -->
  {#if error}
    <p class="text-red-500">エラーが発生しました: {error.message}</p>
  {/if}

  {#if !error}
    {#each poiattr.pois as poi}
      <div class="bg-base-100 shadow mb-2">
        <div class="card-body p-4 md:flex md:flex-row md:w-full md:p-5">
          <p class="text-xs flex items-center md:w-1/5 text-wrap hover:text-gray-500"><a href="/poi/{poi.id}">{poi.id}</a></p>
          <h2 class="card-title md:w-2/5 hover:text-gray-500"><a href="/poi/{poi.id}">{poi.name}</a></h2>
          <div class="flex items-center md:w-1/5">
            <p>
              {#if poi.poi_status === "1"}
                <span
                  class="border border-red-500 px-2 py-1 text-xs rounded text-red-500 text-nowrap"
                  >公開</span
                >
              {:else}
                <span
                  class="border border-gray-500 px-2 py-1 text-xs rounded text-gray-500 text-nowrap"
                  >公開</span
                >
              {/if}
            </p>
            <p class="ml-5 text-sm">2024/12/23 更新</p>
          </div>
          

          <div class="card-actions justify-end md:w-1/5">
            <button
              type="button"
              class="btn btn-accent"
              on:click={() => {
                // 編集ボタンがクリックされたときの処理
                location.href = `/poi/${poi.id}`;
              }}
            >
              <i class="bi bi-pencil" style="font-size: 1rem;"></i>
            </button>

            <button type="button" class="btn">
              <i class="bi bi-trash" style="font-size: 1rem;"></i>
            </button>
          </div>
        </div>
      </div>
    {/each}
  {/if}

  <!-- poiattr が取得できた場合にデータを表示 -->
  <!-- {#if !loading && !error}
    <table class="table w-full">
      <thead>
        <tr class="bg-gray-100">
          <th>店舗ID</th>
          <th>店舗名</th>
          <th>ステータス</th>
          <th>最終更新日</th>
          <th>編集</th>
          <th>削除</th>
        </tr>
      </thead>
      <tbody>
        {#each poiattr.pois as poi}
          <tr>
            <td><a href="/poi/{poi.id}">{poi.id}</a></td>
            <td>{poi.name}</td>
            <td
              >{#if poi.poi_status === "1"}
                <span
                  class="border border-red-500 px-2 py-1 text-xs rounded text-red-500"
                  >公開</span
                >
              {:else}
                <span
                  class="border border-gray-500 px-2 py-1 text-xs rounded text-gray-500"
                  >公開</span
                >
              {/if}</td
            >
            <td>2024/12/23</td>
            <td
              ><button
                type="button"
                class="btn"
                on:click={() => {
                  // 編集ボタンがクリックされたときの処理
                  location.href = `/poi/${poi.id}`;
                }}
              >
                <i class="bi bi-pencil" style="font-size: 1rem;"></i>
              </button></td
            >
            <td
              ><button type="button" class="btn btn-accent">
                <i class="bi bi-trash" style="font-size: 1rem;"></i>
              </button></td
            >
          </tr>
        {/each}
      </tbody>
    </table>
  {/if} -->
</div>
