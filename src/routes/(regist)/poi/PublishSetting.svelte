<script lang="ts">
  import type { ResponsePoi } from '$lib/block/poiattr';

  export let poi: ResponsePoi;
</script>

<div class="mt-10 p-5 bg-white">
  <h3 class="text-l font-bold border-b-2 w-full">公開設定</h3>

  <div class="status mt-5">
    <div class="flex items-start">
      <label class="label flex flex-col md:w-2/5 items-start">
        <span class="label-text">公開・非公開</span>
      </label>
      
      <div class="w-full md:w-3/5 flex justify-start">
        <!-- 公開ラジオボタン -->
        <div class="form-control mr-10">
          <label class="label cursor-pointer" for="radio-public">
            <span class="label-text mr-3">公開</span>
            <input type="radio" name="radio-10" id="radio-public" class="radio checked:bg-blue-500" checked />
          </label>
        </div>
        
        <!-- 非公開ラジオボタン -->
        <div class="form-control">
          <label class="label cursor-pointer" for="radio-private">
            <span class="label-text mr-3">非公開</span>
            <input type="radio" name="radio-10" id="radio-private" class="radio checked:bg-red-500" />
          </label>
        </div>
      </div>
    </div>
    
  </div>
  <div class="timer mt-5">
    <div class="md:flex w-full">
      <label for="start-date" class="label flex flex-col md:w-2/5 items-start">
        <span class="label-text">掲載開始日時</span>
        <p class="text-xs text-gray-500">掲載開始したい日時を入力してください</p>
      </label>
      
      <div class="w-full md:w-3/5 md:flex md:justify-start">
        <!-- Date Input -->
        <input
          type="date"
          id="start-date"
          class="input input-bordered input-primary w-full"
        />
        
        <!-- Time Input -->
        <input
          type="time"
          id="start-time"
          class="input input-bordered input-primary w-full md:ml-5 mt-5 md:mt-0"
        />
      </div>
    </div>
  </div>
</div>