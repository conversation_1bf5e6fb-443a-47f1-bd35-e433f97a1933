<script lang="ts">
  import Meta from "$lib/components/meta.svelte";
  // import type { PageData } from './$types';
  // import type { ResponsePoi } from '$lib/block/poiattr';
  import CommonPoiSetting from "../CommonPoiSetting.svelte";
  import LocationSetting from "../LocationSetting.svelte";
  import IndivPoiSetting from "../IndivPoiSetting.svelte";
  import PublishSetting from "../PublishSetting.svelte";

  // 受け取ったpoiattrを内部で管理するための変数
  let error: any; // エラー管理

  // page.server.ts で定義したデータを取得
  export let data: any;
  console.log(data.poiAttr.data.poi[0]);
  if (data.poiAttr.error) {
    error = data.poiAttr.error;
  }
  let poiattr = { 
    pois: data.poiAttr.data.poi || [],
    hits: data.poiAttr.data.hit || 0
  };
  let poi = poiattr.pois[0] || null;
</script>

<Meta
  title="店舗データ更新"
  description="トップページディスクリプション"
  type="website"
  image="https://example.com/image.jpg"
/>

<h2 class="text-xl font-bold">データの編集</h2>
<p>データの編集を行います。</p>
<!-- エラーが発生した場合の表示 -->
{#if error}
  <p class="text-red-500">エラーが発生しました: {error.message}</p>
{/if}

<!-- poiattr が取得できた場合にデータを表示 -->

{#if poi}
  <div class="mt-10 p-5 bg-white">
    <p>POIデータ: {JSON.stringify(poi)}</p>
  </div>
{/if}

{#if !poi}
  <p>該当するデータが見つかりませんでした。</p>
{/if}

{#if !error && poi}
<CommonPoiSetting {poi} />
<LocationSetting {poi} />
<IndivPoiSetting {poi} />
<PublishSetting {poi} />
{/if}