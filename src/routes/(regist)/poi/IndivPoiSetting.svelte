<script lang="ts">
  import type { ResponsePoi } from '$lib/block/poiattr';

  export let poi: ResponsePoi;
</script>

<div class="mt-10 p-5 bg-white">
    <h3 class="text-l font-bold border-b-2 w-full">その他の設定</h3>
  
    <div class="mt-5">
      <div class="md:flex w-full">
        <label class="label flex flex-col md:w-2/5 items-start">
          <span class="label-text">店舗名よみ</span>
          <p class="text-xs text-gray-500">店舗名よみを入力してください</p>
        </label>
        <input
          type="text"
          class="input input-bordered input-bordered input-primary w-full md:w-3/5"
          placeholder="店舗名よみ"
        />
      </div>
      <p class="text-xs text-gray-500 mt-2 text-right">
        ※文字列で入力してください
      </p>
    </div>
    <div class="mt-5">
      <div class="md:flex w-full">
        <label class="label flex flex-col md:w-2/5 items-start">
          <span class="label-text">郵便番号</span>
          <p class="text-xs text-gray-500">郵便番号を入力してください</p>
        </label>
        <input
          type="text"
          class="input input-bordered input-bordered input-primary w-full md:w-3/5"
          placeholder="郵便番号"
        />
      </div>
      <p class="text-xs text-gray-500 mt-2 text-right">
        ※半角数字で入力してください
      </p>
    </div>
  </div>