<script lang="ts">
  import type { ResponsePoi } from '$lib/block/poiattr';

  export let poi: ResponsePoi;
</script>

<div class="mt-10 p-5 bg-white">
    <h3 class="text-l font-bold border-b-2 w-full">その他の設定</h3>

    <div class="mt-5">
      <div class="md:flex w-full">
        <label for="name-kana" class="label flex flex-col md:w-2/5 items-start">
          <span class="label-text">店舗名よみ</span>
          <p class="text-xs text-gray-500">店舗名よみを入力してください</p>
        </label>
        <input
          id="name-kana"
          type="text"
          class="input input-bordered input-bordered input-primary w-full md:w-3/5"
          placeholder="店舗名よみ"
          bind:value={poi.name_kana}
        />
      </div>
      <p class="text-xs text-gray-500 mt-2 text-right">
        ※文字列で入力してください
      </p>
    </div>

    <div class="mt-5">
      <div class="md:flex w-full">
        <label for="zipcode" class="label flex flex-col md:w-2/5 items-start">
          <span class="label-text">郵便番号</span>
          <p class="text-xs text-gray-500">郵便番号を入力してください</p>
        </label>
        <input
          id="zipcode"
          type="text"
          class="input input-bordered input-bordered input-primary w-full md:w-3/5"
          placeholder="郵便番号"
          bind:value={poi.zipcode}
        />
      </div>
      <p class="text-xs text-gray-500 mt-2 text-right">
        ※半角数字で入力してください
      </p>
    </div>

    <div class="mt-5">
      <div class="md:flex w-full">
        <label for="full-address" class="label flex flex-col md:w-2/5 items-start">
          <span class="label-text">住所</span>
          <p class="text-xs text-gray-500">住所を入力してください</p>
        </label>
        <input
          id="full-address"
          type="text"
          class="input input-bordered input-bordered input-primary w-full md:w-3/5"
          placeholder="住所"
          bind:value={poi.full_address}
        />
      </div>
      <p class="text-xs text-gray-500 mt-2 text-right">
        ※文字列で入力してください
      </p>
    </div>

    <div class="mt-5">
      <div class="md:flex w-full">
        <label for="phone" class="label flex flex-col md:w-2/5 items-start">
          <span class="label-text">電話番号</span>
          <p class="text-xs text-gray-500">電話番号を入力してください</p>
        </label>
        <input
          id="phone"
          type="tel"
          class="input input-bordered input-bordered input-primary w-full md:w-3/5"
          placeholder="電話番号"
          bind:value={poi.phone}
        />
      </div>
      <p class="text-xs text-gray-500 mt-2 text-right">
        ※ハイフンありで入力してください（例：03-1234-5678）
      </p>
    </div>

    <div class="mt-5">
      <div class="md:flex w-full">
        <label for="kencode" class="label flex flex-col md:w-2/5 items-start">
          <span class="label-text">都道府県コード</span>
          <p class="text-xs text-gray-500">都道府県コードを入力してください</p>
        </label>
        <input
          id="kencode"
          type="text"
          class="input input-bordered input-bordered input-primary w-full md:w-3/5"
          placeholder="都道府県コード"
          bind:value={poi.kencode}
        />
      </div>
      <p class="text-xs text-gray-500 mt-2 text-right">
        ※半角数字で入力してください
      </p>
    </div>

    <div class="mt-5">
      <div class="md:flex w-full">
        <label for="kenname" class="label flex flex-col md:w-2/5 items-start">
          <span class="label-text">都道府県名</span>
          <p class="text-xs text-gray-500">都道府県名を入力してください</p>
        </label>
        <input
          id="kenname"
          type="text"
          class="input input-bordered input-bordered input-primary w-full md:w-3/5"
          placeholder="都道府県名"
          bind:value={poi.kenname}
        />
      </div>
      <p class="text-xs text-gray-500 mt-2 text-right">
        ※文字列で入力してください
      </p>
    </div>

    <div class="mt-5">
      <div class="md:flex w-full">
        <label for="citycode" class="label flex flex-col md:w-2/5 items-start">
          <span class="label-text">市区町村コード</span>
          <p class="text-xs text-gray-500">市区町村コードを入力してください</p>
        </label>
        <input
          id="citycode"
          type="text"
          class="input input-bordered input-bordered input-primary w-full md:w-3/5"
          placeholder="市区町村コード"
          bind:value={poi.citycode}
        />
      </div>
      <p class="text-xs text-gray-500 mt-2 text-right">
        ※半角数字で入力してください
      </p>
    </div>

    <div class="mt-5">
      <div class="md:flex w-full">
        <label for="cityname" class="label flex flex-col md:w-2/5 items-start">
          <span class="label-text">市区町村名</span>
          <p class="text-xs text-gray-500">市区町村名を入力してください</p>
        </label>
        <input
          id="cityname"
          type="text"
          class="input input-bordered input-bordered input-primary w-full md:w-3/5"
          placeholder="市区町村名"
          bind:value={poi.cityname}
        />
      </div>
      <p class="text-xs text-gray-500 mt-2 text-right">
        ※文字列で入力してください
      </p>
    </div>

    <div class="mt-5">
      <div class="md:flex w-full">
        <label for="kind" class="label flex flex-col md:w-2/5 items-start">
          <span class="label-text">業種・カテゴリ</span>
          <p class="text-xs text-gray-500">業種・カテゴリを入力してください</p>
        </label>
        <input
          id="kind"
          type="text"
          class="input input-bordered input-bordered input-primary w-full md:w-3/5"
          placeholder="業種・カテゴリ"
          bind:value={poi.kind}
        />
      </div>
      <p class="text-xs text-gray-500 mt-2 text-right">
        ※文字列で入力してください
      </p>
    </div>

    <div class="mt-5">
      <div class="md:flex w-full">
        <label for="crowd-permit-flag" class="label flex flex-col md:w-2/5 items-start">
          <span class="label-text">混雑許可フラグ</span>
          <p class="text-xs text-gray-500">混雑許可フラグを設定してください</p>
        </label>
        <select
          id="crowd-permit-flag"
          class="select select-bordered select-primary w-full md:w-3/5"
          bind:value={poi.crowd_permit_flag}
        >
          <option value="">選択してください</option>
          <option value="0">許可しない</option>
          <option value="1">許可する</option>
        </select>
      </div>
      <p class="text-xs text-gray-500 mt-2 text-right">
        ※混雑情報の表示許可を設定してください
      </p>
    </div>
  </div>