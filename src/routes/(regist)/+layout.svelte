<script lang="ts">
  import Header from "$lib/components/header.svelte";
  import Footer from "$lib/components/footer.svelte";
  import Sidenavi from "$lib/components/sidenavi.svelte";

  import "../../app.css";
  let { children } = $props();
</script>

<Header />
<div class="drawer lg:drawer-open">
  <input id="my-drawer-2" type="checkbox" class="drawer-toggle" />
  <div class="drawer-content flex flex-col m-5 md:m-10">
    <div>
      {@render children()}
    </div>
  </div>
  <Sidenavi />
</div>
<Footer />
